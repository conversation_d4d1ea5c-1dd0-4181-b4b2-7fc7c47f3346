# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.8.3"
  resolved "http://registry.npm.xiaojukeji.com/@babel/code-frame/download/@babel/code-frame-7.8.3.tgz#33e25903d7481181534e12ec0a25f16b6fcf419e"
  integrity sha1-M+JZA9dIEYFTThLsCiXxa2/PQZ4=
  dependencies:
    "@babel/highlight" "^7.8.3"

"@babel/helper-module-imports@^7.0.0":
  version "7.8.3"
  resolved "http://registry.npm.xiaojukeji.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.8.3.tgz#7fe39589b39c016331b6b8c3f441e8f0b1419498"
  integrity sha1-f+OVibOcAWMxtrjD9EHo8LFBlJg=
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-validator-identifier@^7.9.0", "@babel/helper-validator-identifier@^7.9.5":
  version "7.9.5"
  resolved "http://registry.npm.xiaojukeji.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.9.5.tgz#90977a8e6fbf6b431a7dc31752eee233bf052d80"
  integrity sha1-kJd6jm+/a0MafcMXUu7iM78FLYA=

"@babel/highlight@^7.8.3":
  version "7.9.0"
  resolved "http://registry.npm.xiaojukeji.com/@babel/highlight/download/@babel/highlight-7.9.0.tgz#4e9b45ccb82b79607271b2979ad82c7b68163079"
  integrity sha1-TptFzLgreWBycbKXmtgse2gWMHk=
  dependencies:
    "@babel/helper-validator-identifier" "^7.9.0"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@babel/types@^7.8.3":
  version "7.9.6"
  resolved "http://registry.npm.xiaojukeji.com/@babel/types/download/@babel/types-7.9.6.tgz#2c5502b427251e9de1bd2dff95add646d95cc9f7"
  integrity sha1-LFUCtCclHp3hvS3/la3WRtlcyfc=
  dependencies:
    "@babel/helper-validator-identifier" "^7.9.5"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@rollup/plugin-node-resolve@^7.1.3":
  version "7.1.3"
  resolved "http://registry.npm.xiaojukeji.com/@rollup/plugin-node-resolve/download/@rollup/plugin-node-resolve-7.1.3.tgz#80de384edfbd7bfc9101164910f86078151a3eca"
  integrity sha1-gN44Tt+9e/yRARZJEPhgeBUaPso=
  dependencies:
    "@rollup/pluginutils" "^3.0.8"
    "@types/resolve" "0.0.8"
    builtin-modules "^3.1.0"
    is-module "^1.0.0"
    resolve "^1.14.2"

"@rollup/plugin-replace@^2.3.2":
  version "2.3.2"
  resolved "http://registry.npm.xiaojukeji.com/@rollup/plugin-replace/download/@rollup/plugin-replace-2.3.2.tgz#da4e0939047f793c2eb5eedfd6c271232d0a033f"
  integrity sha1-2k4JOQR/eTwute7f1sJxIy0KAz8=
  dependencies:
    "@rollup/pluginutils" "^3.0.8"
    magic-string "^0.25.5"

"@rollup/plugin-typescript@2.1.0":
  version "2.1.0"
  resolved "http://registry.npm.xiaojukeji.com/@rollup/plugin-typescript/download/@rollup/plugin-typescript-2.1.0.tgz#9d4e39cdd152901893285d90b7beb3e2755fb255"
  integrity sha1-nU45zdFSkBiTKF2Qt76z4nVfslU=
  dependencies:
    "@rollup/pluginutils" "^3.0.0"
    resolve "^1.13.1"

"@rollup/pluginutils@^3.0.0", "@rollup/pluginutils@^3.0.8":
  version "3.0.10"
  resolved "http://registry.npm.xiaojukeji.com/@rollup/pluginutils/download/@rollup/pluginutils-3.0.10.tgz#a659b9025920378494cd8f8c59fbf9b3a50d5f12"
  integrity sha1-plm5AlkgN4SUzY+MWfv5s6UNXxI=
  dependencies:
    "@types/estree" "0.0.39"
    estree-walker "^1.0.1"
    picomatch "^2.2.2"

"@samverschueren/stream-to-observable@^0.3.0":
  version "0.3.0"
  resolved "http://registry.npm.xiaojukeji.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.0.tgz#ecdf48d532c58ea477acfcab80348424f8d0662f"
  integrity sha1-7N9I1TLFjqR3rPyrgDSEJPjQZi8=
  dependencies:
    any-observable "^0.3.0"

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.xiaojukeji.com/@types/color-name/download/@types/color-name-1.1.1.tgz#1c1261bbeaa10a8055bbc5d8ab84b7b2afc846a0"
  integrity sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA=

"@types/estree@0.0.39":
  version "0.0.39"
  resolved "http://registry.npm.xiaojukeji.com/@types/estree/download/@types/estree-0.0.39.tgz#e177e699ee1b8c22d23174caaa7422644389509f"
  integrity sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8=

"@types/node@*":
  version "14.0.1"
  resolved "http://registry.npm.xiaojukeji.com/@types/node/download/@types/node-14.0.1.tgz#5d93e0a099cd0acd5ef3d5bde3c086e1f49ff68c"
  integrity sha1-XZPgoJnNCs1e89W948CG4fSf9ow=

"@types/parse-json@^4.0.0":
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz#2f8bb441434d163b35fb8ffdccd7138927ffb8c0"
  integrity sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA=

"@types/prop-types@*":
  version "15.7.5"
  resolved "http://registry.npm.xiaojukeji.com/@types/prop-types/download/@types/prop-types-15.7.5.tgz#5f19d2b85a98e9558036f6a3cacc8819420f05cf"
  integrity sha1-XxnSuFqY6VWANvajysyIGUIPBc8=

"@types/react@16.9.44":
  version "16.9.44"
  resolved "http://registry.npm.xiaojukeji.com/@types/react/download/@types/react-16.9.44.tgz#da84b179c031aef67dc92c33bd3401f1da2fa3bc"
  integrity sha1-2oSxecAxrvZ9ySwzvTQB8dovo7w=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/resolve@0.0.8":
  version "0.0.8"
  resolved "http://registry.npm.xiaojukeji.com/@types/resolve/download/@types/resolve-0.0.8.tgz#f26074d238e02659e323ce1a13d041eee280e194"
  integrity sha1-8mB00jjgJlnjI84aE9BB7uKA4ZQ=
  dependencies:
    "@types/node" "*"

aggregate-error@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.xiaojukeji.com/aggregate-error/download/aggregate-error-3.0.1.tgz#db2fe7246e536f40d9b5442a39e117d7dd6a24e0"
  integrity sha1-2y/nJG5Tb0DZtUQqOeEX191qJOA=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ansi-colors@^3.2.1:
  version "3.2.4"
  resolved "http://registry.npm.xiaojukeji.com/ansi-colors/download/ansi-colors-3.2.4.tgz#e3a3da4bfbae6c86a9c285625de124a234026fbf"
  integrity sha1-46PaS/uubIapwoViXeEkojQCb78=

ansi-escapes@^4.3.0:
  version "4.3.1"
  resolved "http://registry.npm.xiaojukeji.com/ansi-escapes/download/ansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.xiaojukeji.com/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.xiaojukeji.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.xiaojukeji.com/ansi-styles/download/ansi-styles-4.2.1.tgz#90ae75c424d008d2624c5bf29ead3177ebfcf359"
  integrity sha1-kK51xCTQCNJiTFvynq0xd+v881k=
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

any-observable@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.xiaojukeji.com/any-observable/download/any-observable-0.3.0.tgz#af933475e5806a67d0d7df090dd5e8bef65d119b"
  integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.xiaojukeji.com/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.xiaojukeji.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^3.0.1:
  version "3.0.2"
  resolved "http://registry.npm.xiaojukeji.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

builtin-modules@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.xiaojukeji.com/builtin-modules/download/builtin-modules-3.1.0.tgz#aad97c15131eb76b65b50ef208e7584cd76a7484"
  integrity sha1-qtl8FRMet2tltQ7yCOdYTNdqdIQ=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.xiaojukeji.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

chalk@^2.0.0:
  version "2.4.2"
  resolved "http://registry.npm.xiaojukeji.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/chalk/download/chalk-4.0.0.tgz#6e98081ed2d17faab615eb52ac66ec1fe6209e72"
  integrity sha1-bpgIHtLRf6q2FetSrGbsH+YgnnI=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.xiaojukeji.com/clean-stack/download/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.xiaojukeji.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.xiaojukeji.com/cli-truncate/download/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.xiaojukeji.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.xiaojukeji.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.xiaojukeji.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.xiaojukeji.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.xiaojukeji.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

commander@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.xiaojukeji.com/commander/download/commander-5.1.0.tgz#46abbd1652f8e059bddaef99bbdcb2ad9cf179ae"
  integrity sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=

compare-versions@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.xiaojukeji.com/compare-versions/download/compare-versions-3.6.0.tgz#1a5689913685e5a87637b8d3ffca75514ec41d62"
  integrity sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.xiaojukeji.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

cosmiconfig@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.xiaojukeji.com/cosmiconfig/download/cosmiconfig-6.0.0.tgz#da4fee853c52f6b1e6935f41c1a2fc50bd4a9982"
  integrity sha1-2k/uhTxS9rHmk19BwaL8UL1KmYI=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.1.0"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.7.2"

cross-spawn@^7.0.0:
  version "7.0.2"
  resolved "http://registry.npm.xiaojukeji.com/cross-spawn/download/cross-spawn-7.0.2.tgz#d0d7dcfa74e89115c7619f4f721a94e1fdb716d6"
  integrity sha1-0Nfc+nTokRXHYZ9PchqU4f23FtY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

csstype@^3.0.2:
  version "3.0.11"
  resolved "http://registry.npm.xiaojukeji.com/csstype/download/csstype-3.0.11.tgz#d66700c5eacfac1940deb4e3ee5642792d85cd33"
  integrity sha1-1mcAxerPrBlA3rTj7lZCeS2FzTM=

debug@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.xiaojukeji.com/debug/download/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.xiaojukeji.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.xiaojukeji.com/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

elegant-spinner@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/elegant-spinner/download/elegant-spinner-2.0.0.tgz#f236378985ecd16da75488d166be4b688fd5af94"
  integrity sha1-8jY3iYXs0W2nVIjRZr5LaI/Vr5Q=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.xiaojukeji.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://registry.npm.xiaojukeji.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enquirer@^2.3.5:
  version "2.3.5"
  resolved "http://registry.npm.xiaojukeji.com/enquirer/download/enquirer-2.3.5.tgz#3ab2b838df0a9d8ab9e7dff235b0e8712ef92381"
  integrity sha1-OrK4ON8KnYq559/yNbDocS75I4E=
  dependencies:
    ansi-colors "^3.2.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.xiaojukeji.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.xiaojukeji.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

estree-walker@^0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.xiaojukeji.com/estree-walker/download/estree-walker-0.6.1.tgz#53049143f40c6eb918b23671d1fe3219f3a1b362"
  integrity sha1-UwSRQ/QMbrkYsjZx0f4yGfOhs2I=

estree-walker@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.xiaojukeji.com/estree-walker/download/estree-walker-1.0.1.tgz#31bc5d612c96b704106b477e6dd5d8aa138cb700"
  integrity sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA=

execa@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.xiaojukeji.com/execa/download/execa-4.0.1.tgz#988488781f1f0238cd156f7aaede11c3e853b4c1"
  integrity sha1-mISIeB8fAjjNFW96rt4Rw+hTtME=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

figures@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.xiaojukeji.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.xiaojukeji.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.xiaojukeji.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-versions@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.xiaojukeji.com/find-versions/download/find-versions-3.2.0.tgz#10297f98030a786829681690545ef659ed1d254e"
  integrity sha1-ECl/mAMKeGgpaBaQVF72We0dJU4=
  dependencies:
    semver-regex "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.xiaojukeji.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.1.2:
  version "2.1.3"
  resolved "http://registry.npm.xiaojukeji.com/fsevents/download/fsevents-2.1.3.tgz#fb738703ae8d2f9fe900c33836ddebee8b97f23e"
  integrity sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4=

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.xiaojukeji.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.2.tgz#b5fde77f22cbe35f390b4e089922c50bce6ef664"
  integrity sha1-tf3nfyLL4185C04ImSLFC85u9mQ=

get-stream@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.xiaojukeji.com/get-stream/download/get-stream-5.1.0.tgz#01203cdc92597f9b909067c3e656cc1f4d3c4dc9"
  integrity sha1-ASA83JJZf5uQkGfD5lbMH008Tck=
  dependencies:
    pump "^3.0.0"

glob@^7.1.3:
  version "7.1.6"
  resolved "http://registry.npm.xiaojukeji.com/glob/download/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.xiaojukeji.com/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

husky@^4.2.5:
  version "4.2.5"
  resolved "http://registry.npm.xiaojukeji.com/husky/download/husky-4.2.5.tgz#2b4f7622673a71579f901d9885ed448394b5fa36"
  integrity sha1-K092Imc6cVefkB2Yhe1Eg5S1+jY=
  dependencies:
    chalk "^4.0.0"
    ci-info "^2.0.0"
    compare-versions "^3.6.0"
    cosmiconfig "^6.0.0"
    find-versions "^3.2.0"
    opencollective-postinstall "^2.0.2"
    pkg-dir "^4.2.0"
    please-upgrade-node "^3.2.0"
    slash "^3.0.0"
    which-pm-runs "^1.0.0"

import-fresh@^3.1.0:
  version "3.2.1"
  resolved "http://registry.npm.xiaojukeji.com/import-fresh/download/import-fresh-3.2.1.tgz#633ff618506e793af5ac91bf48b72677e15cbe66"
  integrity sha1-Yz/2GFBueTr1rJG/SLcmd+FcvmY=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

indent-string@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/indent-string/download/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.xiaojukeji.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.4"
  resolved "http://registry.npm.xiaojukeji.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.xiaojukeji.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-module@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.xiaojukeji.com/is-module/download/is-module-1.0.0.tgz#3258fb69f78c14d5b815d664336b4cffb6441591"
  integrity sha1-Mlj7afeMFNW4FdZkM2tM/7ZEFZE=

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.xiaojukeji.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.xiaojukeji.com/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.xiaojukeji.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/is-stream/download/is-stream-2.0.0.tgz#bde9c32680d6fae04129d6ac9d921ce7815f78e3"
  integrity sha1-venDJoDW+uBBKdasnZIc54FfeOM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.xiaojukeji.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "http://registry.npm.xiaojukeji.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

lint-staged@^10.2.4:
  version "10.2.4"
  resolved "http://registry.npm.xiaojukeji.com/lint-staged/download/lint-staged-10.2.4.tgz#0ed5d1cf06bdac0d3fbb003931bb6df3771fbf42"
  integrity sha1-DtXRzwa9rA0/uwA5Mbtt83cfv0I=
  dependencies:
    chalk "^4.0.0"
    commander "^5.1.0"
    cosmiconfig "^6.0.0"
    debug "^4.1.1"
    dedent "^0.7.0"
    execa "^4.0.1"
    listr2 "^2.0.2"
    log-symbols "^4.0.0"
    micromatch "^4.0.2"
    normalize-path "^3.0.0"
    please-upgrade-node "^3.2.0"
    string-argv "0.3.1"
    stringify-object "^3.3.0"

listr2@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.xiaojukeji.com/listr2/download/listr2-2.0.2.tgz#35e11e742ee151a8c446d1649792cadf7eb1d780"
  integrity sha1-NeEedC7hUajERtFkl5LK336x14A=
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    chalk "^4.0.0"
    cli-cursor "^3.1.0"
    cli-truncate "^2.1.0"
    elegant-spinner "^2.0.0"
    enquirer "^2.3.5"
    figures "^3.2.0"
    indent-string "^4.0.0"
    log-update "^4.0.0"
    p-map "^4.0.0"
    pad "^3.2.0"
    rxjs "^6.5.5"
    through "^2.3.8"
    uuid "^7.0.2"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.xiaojukeji.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash@^4.17.13:
  version "4.17.15"
  resolved "http://registry.npm.xiaojukeji.com/lodash/download/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
  integrity sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg=

log-symbols@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/log-symbols/download/log-symbols-4.0.0.tgz#69b3cc46d20f448eccdb75ea1fa733d9e821c920"
  integrity sha1-abPMRtIPRI7M23XqH6cz2eghySA=
  dependencies:
    chalk "^4.0.0"

log-update@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/log-update/download/log-update-4.0.0.tgz#589ecd352471f2a1c0c570287543a64dfd20e0a1"
  integrity sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE=
  dependencies:
    ansi-escapes "^4.3.0"
    cli-cursor "^3.1.0"
    slice-ansi "^4.0.0"
    wrap-ansi "^6.2.0"

magic-string@^0.25.5:
  version "0.25.7"
  resolved "http://registry.npm.xiaojukeji.com/magic-string/download/magic-string-0.25.7.tgz#3f497d6fd34c669c6798dcb821f2ef31f5445051"
  integrity sha1-P0l9b9NMZpxnmNy4IfLvMfVEUFE=
  dependencies:
    sourcemap-codec "^1.4.4"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

micromatch@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.xiaojukeji.com/micromatch/download/micromatch-4.0.2.tgz#4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259"
  integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.xiaojukeji.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.xiaojukeji.com/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

ms@^2.1.1:
  version "2.1.2"
  resolved "http://registry.npm.xiaojukeji.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.xiaojukeji.com/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.xiaojukeji.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.xiaojukeji.com/onetime/download/onetime-5.1.0.tgz#fff0f3c91617fe62bb50189636e99ac8a6df7be5"
  integrity sha1-//DzyRYX/mK7UBiWNumayKbfe+U=
  dependencies:
    mimic-fn "^2.1.0"

opencollective-postinstall@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.xiaojukeji.com/opencollective-postinstall/download/opencollective-postinstall-2.0.2.tgz#5657f1bede69b6e33a45939b061eb53d3c6c3a89"
  integrity sha1-Vlfxvt5ptuM6RZObBh61PTxsOok=

p-limit@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.xiaojukeji.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.xiaojukeji.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.xiaojukeji.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pad@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.xiaojukeji.com/pad/download/pad-3.2.0.tgz#be7a1d1cb6757049b4ad5b70e71977158fea95d1"
  integrity sha1-vnodHLZ1cEm0rVtw5xl3FY/qldE=
  dependencies:
    wcwidth "^1.0.1"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.xiaojukeji.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.xiaojukeji.com/parse-json/download/parse-json-5.0.0.tgz#73e5114c986d143efa3712d4ea24db9a4266f60f"
  integrity sha1-c+URTJhtFD76NxLU6iTbmkJm9g8=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"
    lines-and-columns "^1.1.6"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.xiaojukeji.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.xiaojukeji.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.xiaojukeji.com/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picomatch@^2.0.5, picomatch@^2.2.2:
  version "2.2.2"
  resolved "http://registry.npm.xiaojukeji.com/picomatch/download/picomatch-2.2.2.tgz#21f333e9b6b8eaff02468f5146ea406d345f4dad"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.xiaojukeji.com/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

please-upgrade-node@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.xiaojukeji.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

prettier@^2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.xiaojukeji.com/prettier/download/prettier-2.0.5.tgz#d6d56282455243f2f92cc1716692c08aa31522d4"
  integrity sha1-1tVigkVSQ/L5LMFxZpLAiqMVItQ=

pump@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve@^1.13.1, resolve@^1.14.2:
  version "1.17.0"
  resolved "http://registry.npm.xiaojukeji.com/resolve/download/resolve-1.17.0.tgz#b25941b54968231cc2d1bb76a79cb7f2c0bf8444"
  integrity sha1-sllBtUloIxzC0bt2p5y38sC/hEQ=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.xiaojukeji.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.xiaojukeji.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup-plugin-babel@^4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.xiaojukeji.com/rollup-plugin-babel/download/rollup-plugin-babel-4.4.0.tgz#d15bd259466a9d1accbdb2fe2fff17c52d030acb"
  integrity sha1-0VvSWUZqnRrMvbL+L/8XxS0DCss=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    rollup-pluginutils "^2.8.1"

rollup-pluginutils@^2.8.1:
  version "2.8.2"
  resolved "http://registry.npm.xiaojukeji.com/rollup-pluginutils/download/rollup-pluginutils-2.8.2.tgz#72f2af0748b592364dbd3389e600e5a9444a351e"
  integrity sha1-cvKvB0i1kjZNvTOJ5gDlqURKNR4=
  dependencies:
    estree-walker "^0.6.1"

rollup@^2.10.4:
  version "2.10.4"
  resolved "http://registry.npm.xiaojukeji.com/rollup/download/rollup-2.10.4.tgz#ec2f91519c597c214cdf45e50600ed8ea356358e"
  integrity sha1-7C+RUZxZfCFM30XlBgDtjqNWNY4=
  optionalDependencies:
    fsevents "~2.1.2"

rxjs@^6.5.5:
  version "6.5.5"
  resolved "http://registry.npm.xiaojukeji.com/rxjs/download/rxjs-6.5.5.tgz#c5c884e3094c8cfee31bf27eb87e54ccfc87f9ec"
  integrity sha1-xciE4wlMjP7jG/J+uH5UzPyH+ew=
  dependencies:
    tslib "^1.9.0"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.xiaojukeji.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

semver-regex@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/semver-regex/download/semver-regex-2.0.0.tgz#a93c2c5844539a770233379107b38c7b4ac9d338"
  integrity sha1-qTwsWERTmncCMzeRB7OMe0rJ0zg=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

signal-exit@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.xiaojukeji.com/signal-exit/download/signal-exit-3.0.3.tgz#a1410c2edd8f077b08b4e253c8eacfcaf057461c"
  integrity sha1-oUEMLt2PB3sItOJTyOrPyvBXRhw=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.xiaojukeji.com/slice-ansi/download/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.xiaojukeji.com/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

sourcemap-codec@^1.4.4:
  version "1.4.8"
  resolved "http://registry.npm.xiaojukeji.com/sourcemap-codec/download/sourcemap-codec-1.4.8.tgz#ea804bd94857402e6992d05a38ef1ae35a9ab4c4"
  integrity sha1-6oBL2UhXQC5pktBaOO8a41qatMQ=

string-argv@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.xiaojukeji.com/string-argv/download/string-argv-0.3.1.tgz#95e2fbec0427ae19184935f816d74aaa4c5c19da"
  integrity sha1-leL77AQnrhkYSTX4FtdKqkxcGdo=

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.xiaojukeji.com/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

stringify-object@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.xiaojukeji.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.xiaojukeji.com/strip-ansi/download/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.xiaojukeji.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.1.0"
  resolved "http://registry.npm.xiaojukeji.com/supports-color/download/supports-color-7.1.0.tgz#68e32591df73e25ad1c4b49108a2ec507962bfd1"
  integrity sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E=
  dependencies:
    has-flag "^4.0.0"

through@^2.3.8:
  version "2.3.8"
  resolved "http://registry.npm.xiaojukeji.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.xiaojukeji.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.xiaojukeji.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

tslib@^1.9.0:
  version "1.13.0"
  resolved "http://registry.npm.xiaojukeji.com/tslib/download/tslib-1.13.0.tgz#c881e13cc7015894ed914862d276436fa9a47043"
  integrity sha1-yIHhPMcBWJTtkUhi0nZDb6mkcEM=

type-fest@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.xiaojukeji.com/type-fest/download/type-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

typescript@^3.9.2:
  version "3.9.2"
  resolved "http://registry.npm.xiaojukeji.com/typescript/download/typescript-3.9.2.tgz#64e9c8e9be6ea583c54607677dd4680a1cf35db9"
  integrity sha1-ZOnI6b5upYPFRgdnfdRoChzzXbk=

uuid@^7.0.2:
  version "7.0.3"
  resolved "http://registry.npm.xiaojukeji.com/uuid/download/uuid-7.0.3.tgz#c5c9f2c8cf25dc0a372c4df1441c41f5bd0c680b"
  integrity sha1-xcnyyM8l3Ao3LE3xRBxB9b0MaAs=

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.xiaojukeji.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

which-pm-runs@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.xiaojukeji.com/which-pm-runs/download/which-pm-runs-1.0.0.tgz#670b3afbc552e0b55df6b7780ca74615f23ad1cb"
  integrity sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs=

which@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.xiaojukeji.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.xiaojukeji.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.xiaojukeji.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

yaml@^1.7.2:
  version "1.10.0"
  resolved "http://registry.npm.xiaojukeji.com/yaml/download/yaml-1.10.0.tgz#3b593add944876077d4d683fee01081bd9fff31e"
  integrity sha1-O1k63ZRIdgd9TWg/7gEIG9n/8x4=

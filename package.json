{"name": "@didi/dream-utils", "version": "0.1.64-beta.13", "description": "Dream fe team utils", "repository": {"type": "git", "url": "git+https://git.xiaojukeji.com/data-studio-fe/dream-utils"}, "license": "ISC", "maintainers": ["<PERSON><PERSON><PERSON><PERSON>", "yang<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": "<EMAIL>", "keywords": ["utils"], "main": "./lib/utils.cjs.js", "module": "./lib/utils.esm.js", "types": "./lib/index.d.ts", "browser": {"./lib/utils.cjs.js": "./lib/utils.cjs.browser.js", "./lib/utils.esm.js": "./lib/utils.esm.browser.js"}, "files": ["lib"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> lib", "build": "rollup -c", "build:types": "tsc", "postbuild": "yarn run build:types", "prepare": "yarn run build"}, "devDependencies": {"@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "@rollup/plugin-typescript": "2.1.0", "husky": "^4.2.5", "lint-staged": "^10.2.4", "prettier": "^2.0.5", "rimraf": "^3.0.2", "rollup": "^2.10.4", "rollup-plugin-babel": "^4.4.0", "typescript": "^3.9.2", "@types/react": "16.9.44"}, "peerDependencies": {"react": "^16.8.4"}}
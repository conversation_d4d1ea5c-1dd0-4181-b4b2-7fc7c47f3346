import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

// 实时运维
export function getStreamOpsOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('streamops')
  }

  if (isVa) {
    return getVaOrigin('streamops')
  }

  if (isMexico) {
    return getMexicoOrigin('streamops')
  }

  return `http://streamops.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}`
}

export const streamOpsOrigin = getStreamOpsOrigin()

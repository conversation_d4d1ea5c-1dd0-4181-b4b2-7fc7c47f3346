import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getPublishOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('publish')
  }

  if (isVa) {
    return `${getVaOrigin('studio')}/publish`
  }

  if (isMexico) {
    return `${getMexicoOrigin('studio')}/publish`
  }

  return `//studio.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}/publish`
}

/**
 * 获取项目管理平台域名
 */
export const publishOrigin = getPublishOrigin()

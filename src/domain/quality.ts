import { getDreamEnvPart, isASto, isDrill, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getDrillOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getQualityOrigin() {
  if (isASto) {
    return getCloudDreamOrigin()
  }

  if (isVa) {
    return getVaOrigin('quality')
  }

  if (isMexico) {
    return getMexicoOrigin('quality')
  }

  if (isDrill) {
    return getDrillOrigin('quality')
  }

  return `//quality.${getDreamEnvPart()}.${dreamHost}`
}

/**
 * 获取项目管理平台域名
 */
export const qualityOrigin = getQualityOrigin()

import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getStreamOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('stream')
  }

  if (isVa) {
    return `${getVaOrigin('studio')}/stream`
  }

  if (isMexico) {
    return `${getMexicoOrigin('studio')}/stream`
  }

  return `//studio.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}/stream`
}

/**
 * 获取项目管理平台域名
 */
export const streamOrigin = getStreamOrigin()

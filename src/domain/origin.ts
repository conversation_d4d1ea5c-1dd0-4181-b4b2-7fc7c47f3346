import { getDataDreamBasename } from '../basename'
import { DataDreamBasenameValue } from '../interface'
import { domainEnvPart } from './env'
import { dreamHost } from './host'

/**
 * 获取云原生统一完整域名
 */
export function getCloudDreamOrigin(path?: string) {
  const origin = `https://${domainEnvPart}.${dreamHost}`

  if (path) {
    return `${origin}/${path}`
  }

  return origin
}

export const cloudDreamOrigin = getCloudDreamOrigin()

// 弗吉尼亚区域名
export function getVaOrigin(part: string) {
  return `https://${part}.data.${dreamHost}`
}

// 演练环境域名
export function getDrillOrigin(part: string) {
  return `http://${part}-drill.data-pre.${dreamHost}`
}

/**
 * 墨西哥环境域名
 *
 * @param part 模块名
 * @returns 墨西哥环境域名
 */
export function getMexicoOrigin(part: DataDreamBasenameValue) {
  return `https://datadream-mx.intra.didipay.com/${getDataDreamBasename(part)}`
}

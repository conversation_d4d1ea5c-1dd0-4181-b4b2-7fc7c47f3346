import { appEnv, AppEnv, isEast, isEurope } from './env'

/**
 * 获取upm平台域名
 */
export function getUpmOrigin(param = { excludeTest: false }) {
  let envPath = ''
  let domainName = 'xiaojukeji'
  if ([AppEnv.Daily, AppEnv.Pre].includes(appEnv)) {
    envPath = param?.excludeTest ? '' : '-test'
  }

  if (isEast || isEurope) domainName = 'didiglobal'

  return `https://upm${envPath}.${domainName}.com`
}

/**
 * 获取upm平台域名
 */
export const upmOrigin = getUpmOrigin()
// 测试/预发环境将直接跳转国内环境
export const upmExcludeTestOrigin = getUpmOrigin({ excludeTest: true })

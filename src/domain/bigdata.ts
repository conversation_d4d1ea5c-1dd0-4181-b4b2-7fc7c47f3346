import { AppEnv, appEnv, getEnvPart, isMexico, isVa } from './env'
import { getDreamHost } from './host'

// 获取大数据平台链接
const getBigDataOrigin = (): string => {
  if (appEnv === AppEnv.Daily) return `http://bigdata-test.didichuxing.com`

  if (isVa) {
    return `//bigdata.intra.didipay.com`
  }

  if (isMexico) {
    return `//bigdata-mx.intra.didipay.com`
  }

  return `https://bigdata${getEnvPart()}.${getDreamHost('xiaojukeji.com')}`
}

/**
 * 获取大数据平台链接
 * @param testToPre true: pre下返回test地址
 * @returns
 */
export const getTestHandleBigDataOrigin = (ignorePreEnv = false, testToPre = false): string => {
  if (appEnv === AppEnv.Pre) {
    if (!testToPre) {
      return `https://bigdata-pre.intra.xiaojukeji.com`
    } else {
      return `https://bigdata-test.xiaojukeji.com`
    }
  }

  if (isVa) {
    return `//bigdata.intra.didipay.com`
  }

  if (isMexico) {
    return `//bigdata-mx.intra.didipay.com`
  }

  return `https://bigdata${getEnvPart(ignorePreEnv)}.${getDreamHost('xiaojukeji.com')}`
}

// 获取指标管理工具链接
export const indicatorManagementTool = getTestHandleBigDataOrigin(true, true)

export const bigDataOrigin = getBigDataOrigin()

import { dreamHost } from './host'
import { getEnvPart, isPre, isCloud, isVa, isMexico } from './env'

/**
 * 获取omega平台地址
 */
export const omegaOrigin = getOmegaOrigin()

export function getOmegaOrigin(ignorePreEnv = false) {
  if (isPre) {
    return '//omega-test.intra.xiaojukeji.com'
  }

  if (isCloud) {
    return '//omega.cloud.didiglobal.com'
  }

  if (isVa) {
    return '//omega.intra.didipay.com'
  }

  if (isMexico) {
    // TODO: 墨西哥环境域名
    // return ''
  }

  return `//omega${getEnvPart(ignorePreEnv)}.${dreamHost}`
}

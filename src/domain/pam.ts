import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getPamOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('pam')
  }

  if (isVa) {
    return getVaOrigin('pam')
  }

  if (isMexico) {
    return getMexicoOrigin('pam')
  }

  return `http://pam.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}`
}

/**
 * 获取项目管理平台域名
 */
export const pamOrigin = getPamOrigin()

export function getPamOriginWithoutProtocol(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('pam')
  }

  if (isVa) {
    return getVaOrigin('pam')
  }

  if (isMexico) {
    return getMexicoOrigin('pam')
  }

  return `//pam.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}`
}

/**
 * 获取项目管理平台域名，无协议
 */
export const pamOriginWithoutProtocol = getPamOriginWithoutProtocol()

/**
 * 获取项目管理数据源页面链接
 *
 * @param {number} projectId 项目ID
 */
export function getPamSourceLink(projectId?: number) {
  let url = `${pamOrigin}/project/manager?tab=data`
  if (projectId) url += `&projectId=${projectId}`
  return url
}

import { domainEnvPart, isASto, isMexico, isProd, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

function getStudioOrigin() {
  let protocol = 'http:'
  if (isProd) protocol = 'https:'

  if (isASto) {
    return getCloudDreamOrigin('studio')
  }

  if (isVa) {
    return getVaOrigin('studio')
  }

  if (isMexico) {
    return getMexicoOrigin('studio')
  }

  return `${protocol || location.protocol}//studio.${domainEnvPart}.${dreamHost}`
}

/**
 * 获取数据开发平台域名
 */
export const studioOrigin = getStudioOrigin()

import { appEnv, AppEnv, isCloud } from './env'

/**
 * 获取base平台域名
 * @param {boolean} [ignorePreEnv] 部分应用没有预发环境，传递false忽略
 */
export function getBaseOrigin(ignorePreEnv = false) {
  let envPath = ''
  if (appEnv === AppEnv.Daily) envPath = '-test'
  else if (!ignorePreEnv && appEnv === AppEnv.Pre) envPath = '-pre'

  if ([AppEnv.East, AppEnv.Europe].includes(appEnv)) {
    return 'base.intra.didiglobal.com'
  }

  if (isCloud) {
    return 'base.cloud.didiglobal.com'
  }

  return `base${envPath}.xiaojukeji.com`
}

export const baseHostIgnorePre = `//${getBaseOrigin(true)}`
export const baseHost = `//${getBaseOrigin()}`

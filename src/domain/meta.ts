import { dreamHost } from './host'
import { getEnvPart, isVa, isPay, isMexico } from './env'

export function getMetaOrigin() {
  if (isVa) {
    return `meta.intra.didipay.com`
  }

  if (isMexico) {
    return '//meta-mx.intra.didipay.com'
  }

  if (isPay) {
    return `//meta.${dreamHost}`
  }

  return `//meta${getEnvPart()}.${dreamHost}`
}

/**
 * 获取资产管理平台域名
 */
export const metaOrigin = getMetaOrigin()

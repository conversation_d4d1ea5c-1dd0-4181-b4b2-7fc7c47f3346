import { isChina, isEast, isEurope, isMexico, isVa } from './env'

export function getAriusOrigin() {
  if (isChina) {
    return '//arius.didichuxing.com'
  }

  if (isEast) {
    return '//arius.intra.didiglobal.com'
  }

  if (isEurope) {
    return '//arius-de.intra.didiglobal.com'
  }

  if (isVa) {
    return '//arius.intra.didipay.com'
  }

  if (isMexico) {
    return '//arius.datamx.intra.didipay.com'
  }

  return '//arius.didichuxing.com'
}

/**
 * 获取滴滴搜索平台域名
 */
export const ariusOrigin = getAriusOrigin()

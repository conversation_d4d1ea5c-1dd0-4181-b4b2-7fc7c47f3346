import { isEast, isPre, isEurope, isVa, isMexico } from './env'

/**
 * 获取新版kafka系统地址
 */
export const goKafkaLink = (pathname: string, query?: string) => {
  let kafkaDomain = 'https://kafka-manager.intra.xiaojukeji.com'
  if (isPre) {
    kafkaDomain = 'https://kafka-manager-pre.intra.xiaojukeji.com'
  } else if (isEast) {
    kafkaDomain = 'https://kafka-manager-us.intra.didiglobal.com'
  } else if (isEurope) {
    kafkaDomain = 'https://kafka-manager-de.intra.xiaojukeji.com'
  } else if (isVa) {
    return 'https://kafka-manager.intra.didipay.com'
  } else if (isMexico) {
    return 'https://kafka-manager-mx.intra.didiglobal.com'
  }

  return kafkaDomain + pathname + (query ? `?${query}` : '')
}

import { isEast, isPre, isRussia, isEurope } from './env'

/**
 * 获取HBase Service平台域名
 */
function getDHSOrigin() {
  if (isPre) {
    return 'http://10.83.82.12:8090'
  } else if (isRussia) {
    return 'http://dhs.ru.didichuxing.com'
  } else if (isEast) {
    return 'http://dhs.us.didichuxing.com'
  } else if (isEurope) {
    return 'http://dhs.de.didichuxing.com'
  }

  return 'http://dhs.didichuxing.com'
}

export const DHSOrigin = getDHSOrigin()

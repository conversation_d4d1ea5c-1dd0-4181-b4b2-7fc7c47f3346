import { getDreamEnvPart, isASto, isDrill, isEast, isMexico, isProd, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getDrillOrigin, getMexicoOrigin, getVaOrigin } from './origin'

/**
 * 获取调度平台域名
 * @param {boolean} [ignorePreEnv] 部分应用没有预发环境，传递false忽略
 */
export function getScheduleOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('schedule')
  }

  if (isVa) {
    return getVaOrigin('schedule')
  }

  if (isMexico) {
    return getMexicoOrigin('schedule')
  }

  if (isDrill) {
    return getDrillOrigin('schedule')
  }

  return `//schedule.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}`
}

/**
 * 获取调度平台域名
 */
export const scheduleOrigin = getScheduleOrigin()

/**
 * 获取调度平台域名
 */
export function getScheduleTaskUrl(
  code: string,
  version: string | number, // 调度版本
  location: 'taskDetail' | 'taskInstanceDetail' = 'taskDetail',
) {
  if (!code) return null
  if (!version) return null

  const scheduleOrigin = getScheduleOrigin(true)
  let url = `${scheduleOrigin}/schedule_web/redirect_page?from=monitor&location=${location}&params=${
    location === 'taskDetail' ? 'taskCode' : 'code'
  }%3d${code}`

  if (version === 'v3' || version === 3) {
    const host = isEast
      ? 'us.dpp.intra.xiaojukeji.com'
      : isProd
      ? 'dpp.intra.xiaojukeji.com'
      : 'http://*************:3000'
    url = `http://${host}/capricornus/#/taskbatch/info?code=${code}`
  }
  return url
}

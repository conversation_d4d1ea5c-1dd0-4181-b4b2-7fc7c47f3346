import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getSentryOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('sync')
  }

  if (isVa) {
    return getVaOrigin('sync')
  }

  if (isMexico) {
    return getMexicoOrigin('sync')
  }

  return `//sync.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}`
}

/**
 * 获取同步中心平台域名
 */
export const syncCenterOrigin = getSentryOrigin()

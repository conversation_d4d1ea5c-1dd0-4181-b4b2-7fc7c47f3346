import { isEast, isPre, isRussia, isEurope, isCloud } from './env'
import { baseHostIgnorePre } from './base'

/**
 * 获取ddmq系统地址
 */
export const goDdmqLink = (pathname?: string, regionQuery?: string, query?: string) => {
  let host = isCloud ? `${baseHostIgnorePre}/console/ddmq` : `${baseHostIgnorePre}/console/ddmq/v2`
  let regionStr = 'domestic'

  if (isEast) regionStr = 'us'
  if (isRussia) regionStr = 'ru'
  if (isEurope) regionStr = 'de'
  if (isCloud) regionStr = 'domestic'

  return `${host}${pathname || ''}?region=${regionStr}${regionQuery || ''}${query ? '&' + query : ''}`
}

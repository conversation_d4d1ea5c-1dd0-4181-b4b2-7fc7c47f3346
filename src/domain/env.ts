import { isBrowser } from 'src/browser'

export enum AppEnv {
  /** 测试环境 */
  Daily = 'daily',
  /** 预发环境 */
  Pre = 'pre', // 预发环境
  /** 生产环境 */
  Prod = 'prod',
  /** 国内环境 */
  China = 'china',
  /** 俄罗斯环境 */
  Russia = 'russia',
  /** 美东环境 */
  East = 'east',
  /** 欧洲环境 */
  Europe = 'europe',
  /** 滴滴支付环境 */
  Pay = 'pay',
  /** 斯德哥尔摩环境 */
  ASto = 'asto',
  /** 弗吉尼亚区 */
  Va = 'va',
  /** 演练环境 */
  Drill = 'drill',
  /** 墨西哥环境 */
  Mexico = 'mexico',
}

/**
 * 获取当前应用环境
 */
function getAppEnv(ignorePreEnv = false): AppEnv {
  if (!isBrowser) return undefined
  const { host } = window.location
  if (host.includes('-ru.intra.didiglobal.com')) return AppEnv.Russia
  if (host.includes('-de.intra.didiglobal.com')) return AppEnv.Europe
  if (host.endsWith('intra.didiglobal.com')) return AppEnv.East
  if (host.includes('-pay.')) return AppEnv.Pay
  if (host.includes('-daily') || host.includes('-test')) return AppEnv.Daily
  if (host.includes('-asto.cloud.didiglobal.com')) return AppEnv.ASto
  if (host.includes('datadream-mx.intra.didipay.com')) return AppEnv.Mexico
  if (host.includes('.intra.didipay.com')) return AppEnv.Va
  if (host.includes('-drill.data-pre')) return AppEnv.Drill
  if (!ignorePreEnv && host.includes('-pre')) return AppEnv.Pre
  return AppEnv.China
}

export const appEnv = getAppEnv()

/**
 * 获取域名环境部分
 * 目前数据梦工厂下的服务符合该规范
 * Example:
 * 1. [daily] http://{appName}.data-daily.didichuxing.com
 * 2. [pre] http://{appName}.data-pre.didichuxing.com
 * 3. china[production] http://{appName}.data.didichuxing.com
 * 4. russia[production] http://{appName}.data-ru.intra.didiglobal.com
 * 5. east[production] http://{appName}.data.intra.didiglobal.com
 */
export function getEnvPart(ignorePreEnv = false) {
  if (appEnv === AppEnv.Daily) return '-daily'
  if (appEnv === AppEnv.Pay) return '-pay'
  if (!ignorePreEnv && appEnv === AppEnv.Pre) return '-pre'
  if (appEnv === AppEnv.Russia) return '-ru'
  if (appEnv === AppEnv.Europe) return '-de'
  if (appEnv === AppEnv.ASto) return '-asto'
  if (appEnv === AppEnv.Va) return ''
  if (appEnv === AppEnv.Mexico) return ''

  return ''
}

export function getDreamEnvPart(ignorePreEnv = false) {
  return `data${getEnvPart(ignorePreEnv)}`
}

export const domainEnvPart = `data${getEnvPart()}`

/** 测试环境 */
export const isDaily = appEnv === AppEnv.Daily
/** 俄罗斯环境 */
export const isRussia = appEnv === AppEnv.Russia
/** 美东环境 */
export const isEast = appEnv === AppEnv.East
/** 欧洲环境 */
export const isEurope = appEnv === AppEnv.Europe
/** 国内环境 */
export const isChina = appEnv === AppEnv.China
/** 滴滴支付环境 */
export const isPay = appEnv === AppEnv.Pay
/** 弗吉尼亚区 */
export const isVa = appEnv === AppEnv.Va
/** 斯德哥尔摩环境 */
export const isASto = appEnv === AppEnv.ASto
/** 是否生产环境 */
export const isProd = appEnv === AppEnv.Prod || isChina || isEast || isRussia || isEurope || isPay || isASto || isVa
/** 是否云原生环境 */
export const isCloud = appEnv === AppEnv.ASto
/** 是否演练环境 */
export const isDrill = appEnv === AppEnv.Drill
/** 预发环境 */
export const isPre = appEnv === AppEnv.Pre || isDrill
/** 墨西哥环境 */
export const isMexico = appEnv === AppEnv.Mexico

import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getMonitoryOrigin() {
  if (isASto) {
    return getCloudDreamOrigin()
  }

  if (isVa) {
    return getVaOrigin('monitor')
  }

  if (isMexico) {
    return getMexicoOrigin('monitor')
  }

  return `//monitor.${getDreamEnvPart()}.${dreamHost}`
}

/**
 * 获取监控告平台域名
 */
export const monitorOrigin = getMonitoryOrigin()

import { getDreamEnvPart, isASto, isMexico, isVa } from './env'
import { dreamHost } from './host'
import { getCloudDreamOrigin, getMexicoOrigin, getVaOrigin } from './origin'

export function getSentryOrigin(ignorePreEnv = false) {
  if (isASto) {
    return getCloudDreamOrigin('sentry')
  }

  if (isVa) {
    return getVaOrigin('sentry')
  }

  if (isMexico) {
    return getMexicoOrigin('sentry')
  }

  return `//sentry.${getDreamEnvPart(ignorePreEnv)}.${dreamHost}`
}

/**
 * 获取sentry平台域名
 */
export const sentryOrigin = getSentryOrigin()

/**
 * 获取sentry Topic详情页链接
 *
 * @param {string} topicName
 */
export function getSentryTopicLink(topicName: string) {
  return `${sentryOrigin}/topic/detail/${topicName}`
}

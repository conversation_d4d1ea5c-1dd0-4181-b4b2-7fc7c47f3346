import { AppEnv, appEnv } from './env'
import { getDreamHost } from './host'
import { newDataMapOrigin } from './new-data-map'

function getEnvPart() {
  if (appEnv === AppEnv.Daily) return '-test'
  if (appEnv === AppEnv.Russia) return '-ru'
  if (appEnv === AppEnv.Europe) return '-de'
  if (appEnv === AppEnv.ASto) return '-asto'
  return ''
}

// 获取属地图链接
const getDataMapOrigin = (): string => {
  if (appEnv === AppEnv.Daily) return `http://bigdata-test.didichuxing.com`
  return `https://bigdata${getEnvPart()}.${getDreamHost('xiaojukeji.com')}`
}

export const dataMapOrigin = getDataMapOrigin()

// const streamType = ['flink', 'flink_1_10', 'ck', 'druid']

// 获取属地图库表链接
export const getDataMapDBTableUrl = (dbName: string, tableName: string, streamType = 'stream'): string => {
  let path = `/detail?uri=hive://mycluster-tj:${dbName}:${tableName}`

  if (dbName && dbName.endsWith('_stream')) {
    streamType = streamType === 'ck' ? 'ck' : 'stream'
    path = `/detail/${streamType}?uri=stream://${dbName}:${tableName}`
  }

  return `${newDataMapOrigin}${path}`
}

// 获取实时表详情
export const getDataMapStreamTableUrl = (dbName: string, tableName: string): string => {
  const path = `/detail/stream?uri=stream://${dbName}:${tableName}`
  return `${newDataMapOrigin}${path}`
}

interface IDataMapTableUrl {
  type: string
  medium: string
  dbName: string
  tableName: string
}

// 获取数据地图详情url
export const getDataMapDetailUrl = (option: IDataMapTableUrl): string => {
  const { dbName, tableName, medium, type } = option
  const path = `/detail/${medium}?uri=${type}://${dbName}:${tableName}`
  return `${newDataMapOrigin}${path}`
}

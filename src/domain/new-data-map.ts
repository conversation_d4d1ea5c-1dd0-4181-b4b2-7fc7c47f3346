import { dreamHost } from './host'
import { getEnvPart, isASto, isMexico } from './env'
import { getCloudDreamOrigin } from './origin'

/**
 * 获取新版数据地图平台域名
 */

export const getNewDataMapOrigin = (): string => {
  if (isASto) {
    return getCloudDreamOrigin('datamap')
  }

  if (isMexico) {
    return '//datamap-mx.intra.didipay.com'
  }

  return `//datamap${getEnvPart()}.${dreamHost}`
}

export const newDataMapOrigin = getNewDataMapOrigin()

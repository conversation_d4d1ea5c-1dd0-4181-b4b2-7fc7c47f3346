import { dreamHost } from './host'
import { getEnvPart, isCloud } from './env'
import { getCloudDreamOrigin } from './origin'
import { didiMonitorOrigin } from './didi-monitor'

/**
 * 获取值班系统域名
 */

export const getDutySystemOrigin = (): string => {
  if (isCloud) {
    return `${didiMonitorOrigin}/#/alarm/rota`
  }

  return `https://zhiban.didichuxing.com`
}

export const dutySystemOrigin = getDutySystemOrigin()

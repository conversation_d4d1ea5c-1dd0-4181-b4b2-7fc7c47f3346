import { isASto, isChina, isDaily, isDrill, isMexico, isPay, isPre, isVa } from './env'

/**
 * 获取域名主要部分
 */
export function getDreamHost(chinaHost = 'didichuxing.com') {
  const east = 'intra.didiglobal.com'

  if (isASto) {
    return 'cloud.didiglobal.com'
  }

  if (isVa) {
    return 'intra.didipay.com'
  }

  if (isMexico) {
    return 'datadream-mx.intra.didipay.com'
  }

  return isChina || isPre || isDaily || isPay || isDrill ? chinaHost : east
}

export const dreamHost = getDreamHost()

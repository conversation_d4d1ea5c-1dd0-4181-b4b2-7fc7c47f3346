const tenThousand = 10000

export function humanMoneyAsObject(num?: number): { value: number; unit: string } {
  let unit = '元'
  if (num === 0) return { value: 0, unit }
  if (!num) return { value: null, unit: '' }

  let result = num
  try {
    if (result >= tenThousand) {
      result = result / tenThousand
      unit = '万元'

      if (result >= tenThousand) {
        result = result / tenThousand
        unit = '亿元'

        if (result >= tenThousand) {
          result = result / tenThousand
          unit = '万亿元'
        }
      }
    }
    if (isNaN(result)) throw new Error('数据不正确')
    result = Math.round(result * 100) / 100
    if (result >= 100000) result = Math.round(result)
    return { value: result, unit }
  } catch (_) {
    return { value: null, unit: '' }
  }
}

export function humanMoney(num?: number): string {
  const data = humanMoneyAsObject(num)
  if (data.value === null) return '-'
  return `${data.value}${data.unit || ''}`
}

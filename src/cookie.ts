export function delCookie(name: string) {
  if (!document.cookie.includes(name)) return
  const date = new Date()
  date.setTime(date.getTime() - 1 * 24 * 60 * 60 * 1000)

  document.cookie = `${name}=${''}; expires=${date.toUTCString()}`
}

export function setCookie(name: string, value: string, days?: number) {
  delCookie(name)
  if (!days) days = 365
  const date = new Date()
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000)
  document.cookie = `${name}=${value || ''}; expires=${date.toUTCString()}; path=/; domain=.${location.host}`
}

export function getCookie(name: string) {
  const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
  const arr = document.cookie.match(reg)
  return arr ? decodeURIComponent(arr[2]) : ''
}

export function getUsername() {
  return getCookie('mockedUser') || getCookie('mock_username') || getCookie('datadream_username')
}

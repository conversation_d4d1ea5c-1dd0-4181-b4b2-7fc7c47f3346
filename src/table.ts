import { IStringOptions, getStrWidth } from './string'
import React from 'react'

// TODO: 性能监控
// TODO: 性能优化

interface IColumn {
  title: string | React.ReactElement
  dataIndex: string
  line?: number
  width?: number
}

function getWidthOffset(options: IStringOptions) {
  if (options.widthOffset) return options.widthOffset
  if (options.size === 'small') return 20
  if (options.size === 'middle') return 20
  return 40 // 默认值
}

export function smartAttachWidthToColumns(data: any[], _columns: IColumn[], options: IStringOptions) {
  const widthOffset = getWidthOffset(options)
  const columns: any[] = _columns.map((item) => {
    const width = getStrWidth(item.dataIndex, options) + widthOffset
    return { ...item, width }
  })

  data.map((row) => {
    for (const column of columns) {
      const key = column.dataIndex
      const str = row[key]
      const width = Math.ceil(getStrWidth(str, options)) + widthOffset
      if (width > column.width) column.width = width
    }
  })

  let maxLine = 1
  for (const column of columns) {
    maxLine = 1
    let width = column.width
    if (width > 360) {
      width = width / 2
      column.line = 2
      if (maxLine < 2) maxLine = 2

      if (width > 360) {
        width = (width * 2) / 3
        column.line = 3
        if (maxLine < 3) maxLine = 3

        if (width > 360) {
          width = (width * 3) / 4
          column.line = 4
          if (maxLine < 4) maxLine = 4
        }
      }
      if (width < 300) width = 300
    }
    column.width = Math.ceil(width)

    if (maxLine >= 2) {
      for (const column of columns) {
        if ((!column.line || column.line === 1) && column.width > 200) {
          column.width = 200
        }
      }
    }
  }

  return columns
}

export function getTableWidth(columns: { width: number }[]) {
  return columns.reduce((total, { width }) => total + width, 0)
}

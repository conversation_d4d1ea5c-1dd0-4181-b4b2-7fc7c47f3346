export enum TimeMeasureUnit {
  MILLISECOND = 'millisecond',
  SECOND = 'second',
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
}

export interface ITimeMeasure {
  value: number
  label: string
  unit: TimeMeasureUnit
}

/**
 * 时间计量
 * 最多保留2位小数
 * {
 *    value: 10,
 *    label: '10秒'
 *    unit: 'millisecond' | 'minute' | 'hour' | 'day'
 * }
 * @param value
 */
export function timeMeasure(value: number): ITimeMeasure {
  let result: ITimeMeasure = {
    value,
    label: `${value}毫秒`,
    unit: TimeMeasureUnit.MILLISECOND,
  }

  if (value > 1000) {
    value = Math.trunc(value / 1000)
    result = {
      value,
      label: `${value}秒`,
      unit: TimeMeasureUnit.SECOND,
    }

    if (value > 60) {
      value = Math.trunc(value / 60)
      result = {
        value,
        label: `${value}分钟`,
        unit: TimeMeasureUnit.MINUTE,
      }

      if (value >= 100) {
        value = Math.trunc((value * 100) / 60) / 100
        result = {
          value,
          label: `${value}小时`,
          unit: TimeMeasureUnit.HOUR,
        }

        if (value >= 100) {
          value = Math.trunc((value * 100) / 24) / 100
          result = {
            value,
            label: `${value}天`,
            unit: TimeMeasureUnit.DAY,
          }
        }
      }
    }
  }

  return result
}

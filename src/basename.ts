import { isMexico } from './domain/env'
import { DataDreamBasenameType, DataDreamBasenameValue } from './interface'

/**
 * 获取数梦模块路由前缀
 * @param {DataDreamBasenameValue} type 数梦模块名称
 * @returns {string} 数梦模块对应路由前缀
 */
export const getDataDreamBasename = (type: DataDreamBasenameValue) => {
  switch (type) {
    case DataDreamBasenameType.Pam:
      return 'data-pam'
    case DataDreamBasenameType.Studio:
      return 'data-studio'
    case DataDreamBasenameType.Monitor:
      return 'data-monitor'
    case DataDreamBasenameType.Quality:
      return 'data-quality'
    case DataDreamBasenameType.Notebook:
      return 'data-notebook'
    case DataDreamBasenameType.Schedule:
      return 'data-schedule'
    case DataDreamBasenameType.Sentry:
      return 'data-sentry'
    case DataDreamBasenameType.Sync:
      return 'data-sync'
    case DataDreamBasenameType.ApiCenter:
      return 'data-apicenter'
    case DataDreamBasenameType.Streamops:
      return 'data-streamops'
    default:
      return ''
  }
}

/**
 * 获取数梦模块路由前缀 自动判断环境
 */
export const getAutoDataDreamBasename = (type: DataDreamBasenameValue) => {
  if (isMexico) {
    return getDataDreamBasename(type)
  }
  return ''
}

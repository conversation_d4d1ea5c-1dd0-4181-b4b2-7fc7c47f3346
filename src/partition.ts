/* eslint-disable @typescript-eslint/camelcase */

const YEAR_MONTH_DAY_KEYS = ['dt', 'pt', 'ymd', 'ds', 'concat_date']
const YEAR_KEYS = ['year', 'y', 'year_partition']
const MONTH_KEYS = ['month', 'm', 'month_partition']
const DAY_KEYS = ['day', 'd', 'day_partition']
const HOUR_KEYS = ['hour']

const hasKey = (keys: string[], checkKeys: string[]) => {
  for (const key of checkKeys) {
    if (keys.includes(key)) return true
  }
  return false
}

const defaultValue = {
  yearMonthDay: (keys: string[]) => `\${Y=0}-\${M=0}-\${D=${hasKey(keys, HOUR_KEYS) ? '0' : '-1'}}`,
  year: () => '${Y=0}',
  month: (keys: string[]) => `\${M=${hasKey(keys, DAY_KEYS) ? '0' : '-1'}}`,
  day: (keys: string[]) => `\${D=${hasKey(keys, HOUR_KEYS) ? '0' : '-1'}}`,
  hour: () => '${H=-1}',
}

export const PARTITION_KEYS: string[] = [...YEAR_MONTH_DAY_KEYS, ...YEAR_KEYS, ...MONTH_KEYS, ...DAY_KEYS, ...HOUR_KEYS]
export const PARTITION_DEFAULT_VALUE: { [key: string]: (keys?: string[]) => string } = {
  pt: defaultValue.yearMonthDay,
  dt: defaultValue.yearMonthDay,
  ds: defaultValue.yearMonthDay,
  ymd: defaultValue.yearMonthDay,
  concat_date: defaultValue.yearMonthDay,
  year: defaultValue.year,
  y: defaultValue.year,
  year_partition: defaultValue.year,
  month: defaultValue.month,
  m: defaultValue.month,
  month_partition: defaultValue.month,
  day: defaultValue.day,
  d: defaultValue.day,
  day_partition: defaultValue.day,
  hour: defaultValue.hour,
}

const defaultFormat = {
  yearMonthDay: '${Y}-${M}-${D}',
  year: '${Y}',
  month: '${M}',
  day: '${D}',
  hour: '${H}',
}

export const PARTITION_DEFAULT_EXPRESSION: { [key: string]: string } = {
  pt: defaultFormat.yearMonthDay,
  dt: defaultFormat.yearMonthDay,
  ds: defaultFormat.yearMonthDay,
  ymd: defaultFormat.yearMonthDay,
  concat_date: defaultFormat.yearMonthDay,
  year: defaultFormat.year,
  y: defaultFormat.year,
  year_partition: defaultFormat.year,
  month: defaultFormat.month,
  m: defaultFormat.month,
  month_partition: defaultFormat.month,
  day: defaultFormat.day,
  d: defaultFormat.day,
  day_partition: defaultFormat.day,
  hour: defaultFormat.hour,
}

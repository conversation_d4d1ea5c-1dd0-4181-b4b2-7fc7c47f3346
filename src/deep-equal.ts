function isObject(target: any): boolean {
  return Object.prototype.toString.call(target) === '[object Object]'
}

export function deepEqual(origin: any, target: any): boolean {
  try {
    // 对象判断
    if (isObject(origin)) {
      const keys = Object.keys(origin)
      for (const key of keys) {
        const temp = deepEqual(origin[key], target[key])
        if (!temp) return false
      }
      return true
    }

    // 数组判断
    if (Array.isArray(origin)) {
      if (!Array.isArray(target)) return false

      if (origin.length !== target.length) return false

      for (let i = 0; i < origin.length; i++) {
        const temp = deepEqual(origin[i], target[i])
        if (!temp) return false
      }
      return true
    }

    // 值判断, string boolean number
    if (origin === '' && target === undefined) return true // 为ant.design-form做特殊处理
    if (origin !== target && (!origin || !target)) return false
    return origin === target
  } catch (_) {
    return false
  }
}

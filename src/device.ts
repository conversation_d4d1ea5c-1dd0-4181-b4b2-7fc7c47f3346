import { isBrowser } from './browser'

/**
 * 判断当前设备是否为移动端
 * @returns {boolean} 如果是移动端返回 true，否则返回 false
 */
export const isMobileDeviceFn = (): boolean => {
  if (!isBrowser) return false
  const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera

  // 常见移动端设备的关键字
  const mobileRegex = /android|avantgo|blackberry|iemobile|ipad|iphone|ipod|j2me|midp|opera mini|opera mobi|palm|phone|pocket|psp|symbian|tablet|up\.browser|up\.link|webos|windows ce|xda|xiino/i

  return mobileRegex.test(userAgent)
}

/**
 * 是否为移动端
 * @type {boolean}
 */
export const isMobileDevice = isMobileDeviceFn()

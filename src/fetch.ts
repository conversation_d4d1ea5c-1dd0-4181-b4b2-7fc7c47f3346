import { DataDreamBasenameValue } from './interface'
import { isCloud, isMexico } from './domain/env'

/**
 * 获取接口前缀，用于兼容云原生统一域名 (废弃)
 * Nginx中先rewrite module 为空，再转发到后端对应机器中。
 * @deprecated 已废弃，请使用 dataDreamApiPrefix 代替
 * @param {string} module 功能模块
 * @param {string} prefix 原有接口前缀
 */
export const getFetchPrefix = (module: string, prefix = '') => (isCloud ? `/${module}/${prefix}` : `/${prefix}`)

/**
 * 获取 window.location.pathname 的第 index 个值，
 * @param {number} index 索引
 */
export const getPathnameByIndex = (index: number) => {
  return window.location.pathname.split('/')[index]
}

/**
 * 获取 window.location.pathname 的第 1 个值
 */
export const firstPathname = getPathnameByIndex(1)

/**
 * 获取数梦接口前缀，用于兼容墨西哥数梦接口前缀
 * @param {string} customPrefix 自定义接口前缀，默认不填 优先级最高
 * @returns {string} 数梦接口前缀 默认是/data-${basename}
 */
export const getDataDreamApiPrefix = (customPrefix = '') => {
  if (customPrefix) {
    return customPrefix
  }

  if (!isMexico) {
    return ''
  }

  const basename = getPathnameByIndex(1) as DataDreamBasenameValue

  return `/${basename}`
}

/**
 * 获取数据梦接口前缀
 */
export const dataDreamApiPrefix = getDataDreamApiPrefix()

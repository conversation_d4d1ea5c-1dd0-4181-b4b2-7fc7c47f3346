const tenThousand = 10000

export function humanNumAsObject(num: number): { value: number; unit: string } {
  if (num === null || num === undefined) return { value: null, unit: '' }

  if (num === 0) return { value: 0, unit: '' }

  let temp = num
  let unit = ''
  try {
    if (temp >= tenThousand) {
      temp = temp / tenThousand
      unit = '万'

      if (temp >= tenThousand) {
        temp = temp / tenThousand
        unit = '亿'

        if (temp >= tenThousand) {
          temp = temp / tenThousand
          unit = '万亿'
        }
      }
    }
    let value = Number(temp)
    if (isNaN(value)) throw new Error('数据不正确')
    value = Math.round(value * 100)
    value = value >= 100000 ? Math.round(value / 100) : value / 100
    return { value, unit }
  } catch (_) {
    return { value: Number(num) || null, unit: '' }
  }
}

export function humanNum(
  num: number,
  params?: { useGrouping?: boolean; unit?: boolean; locales?: string | string[] },
): string {
  if (!num && num !== 0) return '-'

  if (params && params.useGrouping && !params.unit) {
    return Number(num).toLocaleString(params?.locales || 'hanidec', { useGrouping: true })
  }

  const data = humanNumAsObject(num)

  if (data.value === null) return '-'

  if (params && params.useGrouping) {
    return `${Number(data.value).toLocaleString(params?.locales || 'hanidec', { useGrouping: true })}${data.unit || ''}`
  }

  return `${data.value}${data.unit || ''}`
}

const a = [...Array(128 - 33).keys()].map((i) => String.fromCharCode(i + 32))

export interface IStringOptions {
  fontSize?: number
  fontFamily?: string
  widthOffset?: number // 默认值 40
  size?: 'small' | 'middle' | 'default' // 最终决定widthOffset选项，优先级低于widthOffset
}

interface CacheWidthMap {
  [key: string]: {
    [key: string]: Map<string, number>
  }
}

interface CacheWidthObj {
  [key: string]: {
    [key: string]: {
      [key: string]: number
    }
  }
}

let cacheOptions: IStringOptions = {}
let cacheWidth: CacheWidthMap = getCacheWidthToLocal()
const cacheKey = 'cacheCharacterWidth'

/**
 * 将数据缓存到localStorage
 * @param data 要缓存的数据
 */
function cacheWidthToLocal(data: CacheWidthMap) {
  const cacheWidth: CacheWidthObj = {}
  for (const key of Object.keys(data)) {
    const subData = data[key]
    cacheWidth[key] = {}
    for (const subKey of Object.keys(subData)) {
      cacheWidth[key][subKey] = Object.fromEntries(subData[subKey])
    }
  }
  localStorage.setItem('cacheCharacterWidth', JSON.stringify(cacheWidth))
}

/**
 * 从localStorage取出字符宽度缓存
 */
function getCacheWidthToLocal() {
  try {
    const data = localStorage.getItem('cacheCharacterWidth')
    if (!data) return {}

    const cacheWidth: CacheWidthMap = {}
    const _cacheWidth = JSON.parse(data)
    for (const key of Object.keys(_cacheWidth)) {
      const _subData = _cacheWidth[key]
      cacheWidth[key] = {}
      for (const subKey of Object.keys(_subData)) {
        cacheWidth[key][subKey] = new Map(Object.entries(_subData[subKey]))
      }
    }
    return cacheWidth
  } catch {}
  return {}
}

function getSingleStringWidth(rootDom: HTMLSpanElement, str: string, fontSize: number, fontFamily: string) {
  const dom = document.createElement('span')
  dom.style.fontFamily = fontFamily
  dom.style.fontSize = fontSize + 'px'
  dom.textContent = str
  rootDom.appendChild(dom)
  const width = dom.getBoundingClientRect().width
  dom.remove()
  return width
}

/**
 * @param fontSize 字体大小
 * @param fontFamily 字体名称
 */
function generateStrWidth({ fontSize, fontFamily }: IStringOptions) {
  if (cacheWidth[fontFamily] && cacheWidth[fontFamily][fontSize]) return cacheWidth[fontFamily][fontSize]

  if (!cacheWidth[fontFamily]) cacheWidth[fontFamily] = {}

  const rootDom = document.createElement('span')
  rootDom.style.visibility = 'hidden'
  rootDom.style.position = 'absolute'
  rootDom.style.left = '-10000px'
  document.body.appendChild(rootDom)

  const result = new Map<string, number>()
  for (const item of a) {
    const width = getSingleStringWidth(rootDom, item, fontSize, fontFamily)
    result.set(item, width)
  }

  result.set('中文', getSingleStringWidth(rootDom, '你', fontSize, fontFamily)) // 所有中文的宽度都统一
  result.set(' ', result.get('中文')) // 浏览器对空格不显示宽度
  rootDom.remove()
  cacheWidth[fontFamily][fontSize] = result

  cacheWidthToLocal(cacheWidth)

  return result
}

/**
 * @param string
 */
export function getStrWidth(str: string | number, options: IStringOptions) {
  if (!str) return 0
  if (typeof str !== 'string') str = String(str)
  options = { ...cacheOptions, ...options }
  const tempWidth = generateStrWidth(options)

  let width = 0
  for (const s of str) {
    const temp = tempWidth.get(s)
    if (temp) width += temp
    else width += tempWidth.get('中文')
  }
  return width
}

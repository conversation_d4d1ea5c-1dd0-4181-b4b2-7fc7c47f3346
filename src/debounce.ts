export const debounce = (callback: (...args: any[]) => void, waitMilliseconds = 200, isImmediate = false) => {
  let timeoutID: number = undefined

  return function (this: any, ...args: any[]): void {
    /* eslint-disable @typescript-eslint/no-this-alias */
    const context = this
    const shouldCallNow = isImmediate && !timeoutID

    const doLater = (): void => {
      timeoutID = undefined
      if (!shouldCallNow) callback.apply(context, args)
    }

    if (timeoutID) window.clearTimeout(timeoutID)

    timeoutID = window.setTimeout(doLater, waitMilliseconds)

    if (shouldCallNow) callback.apply(context, args)
  }
}

interface NumberOptions {
  /** 是否启用模糊匹配，默认值为 false */
  fuzzyMatch?: boolean;
}

/**
 * 判断一个值是否为数字。
 *
 * @param value - 要检查的值，可以是任何类型。
 * @param options - 配置选项对象，包含以下属性：
 *  - fuzzyMatch?: boolean - 是否启用模糊匹配，默认为 false。如果启用，
 *    函数将接受字符串形式的数字（例如 '123'）并将其视为有效数字。
 *
 * @returns boolean - 如果是数字，返回 true；否则返回 false。
 *
 * @example
 * isNumber(123); // 精准匹配，返回 true
 * isNumber('123'); // 精准匹配，返回 false，因为字符串 '123' 不是数字
 * isNumber('123', { fuzzyMatch: true }); // 启用模糊匹配，返回 true，因为 '123' 可以被转换为数字
 * isNumber('abc', { fuzzyMatch: true }); // 模糊匹配，返回 false，因为 'abc' 不能被转换为数字
 */
export function isNumber(value: any, options: NumberOptions = {}): boolean {
  const { fuzzyMatch = false } = options;

  if (fuzzyMatch) {
    // 模糊匹配：允许字符串形式的数字（例如 '123'）
    return !Number.isNaN(Number(value));
  } else {
    // 精准匹配：要求是 'number' 类型并且不是 NaN
    return typeof value === 'number' && !Number.isNaN(value);
  }
}


import { getCookie } from './cookie'

export const MIN_LIFECYCLE_TTL = 0
export const MAX_LIFECYCLE_TTL = 1830

export const LIFECYCLE_DEFAULT = 1
export const LIFECYCLE_FOREVER = -1
export const LIFECYCLE_RECOMMEND = -2
export const LIFECYCLE_CUSTOM = -3

const isEnglish = getCookie('lang') === 'en'

export const LIFECYCLE_LIST = [
  { value: 7, label: isEnglish ? '7 days' : '7天' },
  { value: 15, label: isEnglish ? '15 days' : '15天' },
  { value: 33, label: isEnglish ? '33 days' : '33天' },
  { value: 93, label: isEnglish ? '93 days' : '93天' },
  { value: 183, label: isEnglish ? '183 days' : '183天' },
  { value: 369, label: isEnglish ? '369 days' : '369天' },
  { value: LIFECYCLE_FOREVER, label: isEnglish ? 'forever' : '永久' },
  { value: LIFECYCLE_CUSTOM, label: isEnglish ? 'custom (day)' : '自定义(天)' },
]

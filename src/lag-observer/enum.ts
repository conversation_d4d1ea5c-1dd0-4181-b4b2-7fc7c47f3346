export enum EntryTypeEnum {
  longTask = 'longtask',
  layoutShift = 'layout-shift',
  largestContentfulPaint = 'largest-contentful-paint',
  firstInput = 'first-input',
  event = 'event',
  mark = 'mark',
  measure = 'measure',
  navigation = 'navigation',
  paint = 'paint',
  resource = 'resource',
}

export enum EventTypeEnum {
  click = 'click',
  dbClick = 'dbclick',
}

export enum ClickOrDbClickDetailEnum {
  click = 1,
  dbClick = 2,
}

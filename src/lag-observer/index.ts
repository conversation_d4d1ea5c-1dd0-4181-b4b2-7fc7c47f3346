import { DEFAULT_OPTIONS, maxValidDuration } from './constant'
import { ClickOrDbClickDetailEnum, EntryTypeEnum, EventTypeEnum } from './enum'
import { IEventItem, ILagObserverOptions, lagItem } from './type'
import { getDomPath, uuid } from './utils'

// 因延迟上报，所以存在漏报可能性，漏报数量最大为callEveryNth条，
// sendBeacon 不支持在header中填入参数, omega不支持，
// 如要求精确上报，可以考虑存储到本地，用户下次打开页面再进行上报，但存在数据实效问题
export class LagObserver {
  options: ILagObserverOptions
  eventList: IEventItem[] = []
  lagList: lagItem[] = []
  timeoutId: number
  observer: PerformanceObserver

  constructor(options: ILagObserverOptions) {
    if (typeof options !== 'object') {
      throw Error('LagObserver: 请配置参数')
    }

    this.options = {
      ...DEFAULT_OPTIONS,
      ...options,
    }
  }

  install = () => {
    if (!window.PerformanceObserver?.supportedEntryTypes?.length) {
      return
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    // 如safari 即不支持
    if (!window.PerformanceLongTaskTiming) {
      return
    }

    this.observe()
    this.bindEvents()
  }

  observe = () => {
    const observer = new window.PerformanceObserver(this.observerCallback)

    this.observer = observer

    observer.observe({
      type: EntryTypeEnum.longTask,
      // 开启缓存，开启监听之前的卡顿行为也会被记录下来，如果配置entryTypes， buffered不生效。
      buffered: true,
    })
  }

  observerCallback = (list: PerformanceObserverEntryList) => {
    const { durationThreshold, callEveryNth, callEveryNms, onEveryLag } = this.options
    const entries = list.getEntries()

    if (entries?.length) {
      entries.forEach((entry: PerformanceEntry & { key: string }) => {
        if (entry.duration > durationThreshold && entry.duration < maxValidDuration) {
          try {
            entry.key = uuid()

            onEveryLag?.(entry)
            this.record(entry)
          } catch (err) {
            console.log(err)
          }
        }
      })
    }

    const lagListLen = this.lagList.length

    // 如果大于阈值callEveryNth次，通知业务进行上报
    if (lagListLen >= callEveryNth) {
      this.notice()
    }

    // 如果小于阈值，则存储，callEveryNms毫秒之后再进行上报
    if (lagListLen && lagListLen < callEveryNth && !this.timeoutId) {
      this.timeoutId = window.setTimeout(() => {
        this.notice()
      }, callEveryNms)
    }
  }

  parseEventList = (start_time: number) => {
    return this.eventList
      .map((item, idx) => {
        const next = this.eventList[idx + 1]

        // 如果后一个记录的事件是双击事件，则当前单机事件无效，直接移除
        if (
          next &&
          next.event?.detail === ClickOrDbClickDetailEnum.dbClick &&
          next.event?.target === item.event?.target
        ) {
          return undefined
        }

        if (item.start_time < start_time) {
          const { event } = item

          return {
            start_time: item.start_time,
            // 区分单击/双击事件
            event_type:
              item.event_type === EventTypeEnum.click
                ? event.detail === ClickOrDbClickDetailEnum.dbClick
                  ? EventTypeEnum.dbClick
                  : EventTypeEnum.click
                : undefined,
            target_selector: getDomPath(event.target as Element).join(' '),
          }
        }
      })
      .filter(Boolean)
  }

  record = (entry: PerformanceEntry & { key: string }) => {
    // 根据performance.timeOrigin计算卡顿发生时间
    const start_time = entry.startTime + performance.timeOrigin

    this.lagList.push({
      key: entry.key, // 卡顿事件唯一标识
      duration: entry.duration, // 卡顿时长
      start_time, // 卡顿开始时间
      entry_type: entry.entryType as EntryTypeEnum,
      url: location.href,
      // 卡顿前的用户动作(目前为点击事件)
      event_list: this.parseEventList(start_time),
    })
    // 为什么没有把最后一个事件当成卡顿原因：因为有可能用户点击后触发了请求，或者其他原因原因导致的异步，经过一段时间才发生卡顿行为，
    // 在这段时间，用户还是可能有其他点击操作
    // 事件被消费后没有马上清除掉，是因为可能存在某个事件触发了比较多的状态变更/js逻辑，导致多次卡顿
  }

  bindEvents = () => {
    document.addEventListener(EventTypeEnum.click, (e) => {
      // 如果超过缓存阈值15个，保留最后14个
      if (this.eventList.length >= this.options.eventsCacheNumber) {
        this.eventList = this.eventList.slice(1)
      }

      // 点击时只缓存，不处理，避免性能问题
      this.eventList.push({
        event: e,
        start_time: +new Date(),
        event_type: EventTypeEnum.click,
      })
    })
  }

  notice = () => {
    this.options.onLag?.([...this.lagList])
    this.lagList = []
    clearTimeout(this.timeoutId)
    this.timeoutId = undefined
  }

  stop = () => {
    this.observer && this.observer.disconnect()
  }
}

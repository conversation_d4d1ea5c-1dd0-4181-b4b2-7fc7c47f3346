import { lagItem } from './type'

export const DEFAULT_OPTIONS = {
  durationThreshold: 200,
  onLag: (lagInfoList: lagItem[]) => {
    console.log(lagInfoList)
  },
  callEveryNth: 10, // 默认每10个卡顿信息一组上报
  callEveryNms: 1000 * 60 * 1, // 当发生一次卡顿后，如果后续没有集满10个，1分钟后直接上报(具体上报数量按1分钟时的算)
  eventsCacheNumber: 5, // 将发生卡顿前的5次点击/双击事件存储上报
}

export const maxValidDuration = 300 * 1000 // 上报数据中发现异常数据，卡住超过100秒，认为是异常数据，丢弃

import { EntryTypeEnum, EventTypeEnum } from './enum'

// 数据库字段规范为下划线
export interface ILagObserverOptions {
  onLag: (lagList: lagItem[]) => void
  onEveryLag?: (entry: PerformanceEntry) => void
  durationThreshold?: number
  callEveryNth?: number
  callEveryNms?: number
  eventsCacheNumber?: number
}

export interface IEventItem {
  event: MouseEvent
  start_time: number
  event_type: EventTypeEnum
}

export interface lagItem {
  duration: number
  start_time: number
  entry_type: EntryTypeEnum
  url: string
  event_list: IReportEvenItem[]
  key: string
}

export interface IReportEvenItem {
  start_time: number
  event_type: EventTypeEnum
  target_selector: string
}

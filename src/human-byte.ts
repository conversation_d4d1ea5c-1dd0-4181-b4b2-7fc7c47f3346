const rate = 1024

export function humanByteAsObject(num: number, defaultUnit: 'KB' | 'Byte' = 'Byte'): { value: number; unit: string } {
  if (num === null || num === undefined) return { value: null, unit: '' }
  if (Number(num) === 0) return { value: 0, unit: 'Byte' }

  const isNegative = Number(num) < 0
  num = isNegative ? -Number(num) : num

  if (defaultUnit === 'KB') num = num * rate

  let value = 0
  let unit = 'Byte'

  try {
    if (num > Number.MAX_SAFE_INTEGER) {
      value = Number(num / rate / rate / rate / rate)

      if (value >= rate) {
        value = value / rate
        unit = 'PB'

        if (value >= rate) {
          value = value / rate
          unit = 'EB'

          if (value >= rate) {
            value = value / rate
            unit = 'ZB'

            if (value >= rate) {
              value = value / rate
              unit = 'YB'

              if (value >= rate) {
                value = value / rate
                unit = 'NB'
              }
            }
          }
        }
      }
    } else {
      value = Number(num)

      if (value >= rate) {
        value = value / rate
        unit = 'KB'

        if (value >= rate) {
          value = value / rate
          unit = 'MB'

          if (value >= rate) {
            value = value / rate
            unit = 'GB'

            if (value >= rate) {
              value = value / rate
              unit = 'TB'

              if (value >= rate) {
                value = value / rate
                unit = 'PB'
              }
            }
          }
        }
      }
    }

    if (isNaN(value)) throw new Error('数据不正确')
    value = Math.round(value * 100) / 100
    if (value >= 100000) value = Math.round(value)
    if (isNegative) value = -value
    return { value, unit }
  } catch (_) {
    return { value: null, unit: '' }
  }
}

export function humanByte(num: number, defaultUnit: 'KB' | 'Byte' = 'Byte'): string {
  const data = humanByteAsObject(num, defaultUnit)
  if (data.value === null) return '-'
  return `${data.value}${data.unit || ''}`
}

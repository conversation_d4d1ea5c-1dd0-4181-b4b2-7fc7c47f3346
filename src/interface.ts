/**
 * 项目模块路由枚举
 */
export enum DataDreamBasenameType {
  /** 项目管理 */
  Pam = 'pam',
  /** 数据开发 */
  Studio = 'studio',
  /** 监控告警 */
  Monitor = 'monitor',
  /** 数据质量 */
  Quality = 'quality',
  /** notebook */
  Notebook = 'notebook',
  /** 调度 */
  Schedule = 'schedule',
  /** sentry */
  Sentry = 'sentry',
  /** 同步中心 */
  Sync = 'sync',
  /** api-center */
  ApiCenter = 'api-center',
  /** 实时运维 */
  Streamops = 'streamops',
}

/**
 * 数梦模块路由枚举(值)
 */
export type DataDreamBasenameValue =
  | 'pam'
  | 'studio'
  | 'monitor'
  | 'quality'
  | 'notebook'
  | 'schedule'
  | 'sentry'
  | 'sync'
  | 'api-center'
  | 'streamops'

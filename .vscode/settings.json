{"files.associations": {"*.css": "postcss"}, "editor.tabSize": 2, "editor.formatOnSave": true, "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always", "typescript.extension.sortImports.omitSemicolon": true, "typescript.extension.sortImports.sortOnSave": true, "typescript.extension.sortImports.maxNamedImportsInSingleLine": 20, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib"}